# PostgreSQL OMOP CDM Database Setup Guide

This step-by-step guide explains how to set up a PostgreSQL database for the OMOP Common Data Model (CDM) v5.4. This approach is recommended for production environments and follows the official OHDSI guidelines.

## Official Resources

- [OHDSI CommonDataModel Repository](https://github.com/OHDSI/CommonDataModel)
- [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
- [DDL Files for CDM v5.4](https://github.com/OHDSI/CommonDataModel/tree/v5.4.0/inst/ddl/5.4/postgresql)

## Prerequisites

Before starting, ensure you have:

- PostgreSQL 13.0 or higher installed
- Admin access to PostgreSQL
- At least 50GB of free disk space (more if loading full vocabularies)
- Command-line access to run scripts
- `wget` or similar tool to download files

## Step 1: Install PostgreSQL (if not already installed)

### On Ubuntu/Debian:
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
```

### On macOS (using Homebrew):
```bash
brew install postgresql
brew services start postgresql
```

### On Windows:
Download and install from [PostgreSQL official website](https://www.postgresql.org/download/windows/)

## Step 2: Create Database and User

### Connect to PostgreSQL

The connection method depends on your PostgreSQL installation:

**For macOS with Homebrew (PostgreSQL 14):**
```bash
# Check if PostgreSQL is running
ps aux | grep postgres

# Connect using full path (if psql not in PATH)
/opt/homebrew/opt/postgresql@14/bin/psql -U $(whoami) -d postgres

# Or if psql is in PATH
psql -U $(whoami) -d postgres
```

**For Linux systems:**
```bash
sudo -u postgres psql
```

### Create Database and User

Execute the following SQL commands in order:

```sql
-- 1. Create user first (allows setting as owner immediately)
CREATE USER omop WITH PASSWORD 'omop_secure_2024';

-- 2. Create database with omop as owner
CREATE DATABASE omop_cdm OWNER omop;

-- 3. Grant explicit privileges (redundant but good practice)
GRANT ALL PRIVILEGES ON DATABASE omop_cdm TO omop;

-- 4. Verify creation
\l

-- 5. Exit PostgreSQL
\q
```

### Verify Setup

Test connectivity with the new user:

```bash
# Test connection (replace with your PostgreSQL path if needed)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT current_database(), current_user;"
```

Expected output:
```
 current_database | current_user
------------------+--------------
 omop_cdm         | omop
```

### Troubleshooting

**If `psql: command not found`:**
```bash
# Find PostgreSQL installation
find /opt -name psql 2>/dev/null
# Or for Homebrew specifically
ls /opt/homebrew/opt/postgresql*/bin/psql
```

**If connection fails:**
```bash
# Check if PostgreSQL is running
brew services list | grep postgresql
# Start if needed
brew services start postgresql@14
```

**If user already exists:**
```sql
-- Drop and recreate if needed
DROP DATABASE IF EXISTS omop_cdm;
DROP USER IF EXISTS omop;
-- Then repeat creation steps
```

## Step 3: Download OMOP CDM DDL Scripts

Create a directory for the DDL scripts and download them from the official OHDSI repository:

```bash
# Create directory for DDL scripts
mkdir -p scripts/ddl/postgresql

# Download DDL scripts
## Create tables
wget https://github.com/OHDSI/CommonDataModel/raw/v5.4.0/inst/ddl/5.4/postgresql/OMOPCDM_postgresql_5.4_ddl.sql -O scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_ddl.sql
## Define primary keys
wget https://github.com/OHDSI/CommonDataModel/raw/v5.4.0/inst/ddl/5.4/postgresql/OMOPCDM_postgresql_5.4_primary_keys.sql -O scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_primary_keys.sql
## Create indices for performance
wget https://github.com/OHDSI/CommonDataModel/raw/v5.4.0/inst/ddl/5.4/postgresql/OMOPCDM_postgresql_5.4_indices.sql -O scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_indices.sql
## Define constraints (foreign keys)
wget https://github.com/OHDSI/CommonDataModel/raw/v5.4.0/inst/ddl/5.4/postgresql/OMOPCDM_postgresql_5.4_constraints.sql -O scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_constraints.sql
```

## Step 4: Modify the Scripts to Replace Schema Placeholder

The DDL scripts contain a placeholder `@cdmDatabaseSchema` that needs to be replaced with your actual schema name (typically 'public' for PostgreSQL):

```bash
# Replace schema placeholder in all scripts
# For public schema:
sed -i 's/@cdmDatabaseSchema/public/g' scripts/ddl/postgresql/*.sql

# For custom schema (if you created one):
# sed -i 's/@cdmDatabaseSchema/omop/g' scripts/ddl/postgresql/*.sql
```

> **Note for macOS users**: The `sed` command is slightly different. Use:
> ```bash
> sed -i '' 's/@cdmDatabaseSchema/public/g' scripts/ddl/postgresql/*.sql
> ```

## Step 5: Execute the Scripts in the Correct Order

Now execute the scripts in the following order:

```bash
# 1. Create tables
psql -U omop -d omop_cdm -f scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_ddl.sql

# 2. Add primary keys
psql -U omop -d omop_cdm -f scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_primary_keys.sql

# 3. Create indices
psql -U omop -d omop_cdm -f scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_indices.sql

# 4. Add constraints (foreign keys)
psql -U omop -d omop_cdm -f scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_constraints.sql
```

> **Note**: You may be prompted for the password you set for the 'omop' user.

## Step 6: Verify the Installation

You can verify that the tables were created correctly by connecting to the database and listing the tables:

```bash
psql -U omop -d omop_cdm -c "\dt"
```

This should display a list of all the OMOP CDM tables that were created.

## Step 7: Configure Environment Variables

Update your `.env` file with the database connection details:

```
# OMOP Database Configuration
OMOP_DB_HOST=localhost
OMOP_DB_PORT=5432
OMOP_DB_NAME=omop_cdm
OMOP_DB_SCHEMA=public  # or your custom schema name
OMOP_DB_USERNAME=omop
OMOP_DB_PASSWORD=your_secure_password
```

## Step 8: Load OMOP Vocabularies

The OMOP CDM requires standardized vocabularies to function properly. Follow these steps to load them:

1. **Register and download vocabularies from [Athena](https://athena.ohdsi.org/)**:
   - Create an account on Athena
   - Select the vocabularies you need (at minimum: SNOMED, LOINC, RxNorm)
   - Download the vocabulary files (this may take some time)

2. **Extract the downloaded files** to a directory, e.g., `data/vocabulary/`

3. **Load the vocabulary files into PostgreSQL**:
   ```bash
   # Navigate to the vocabulary directory
   cd data/vocabulary/
   
   # Load each vocabulary file
   # Example for the CONCEPT table:
   psql -U omop -d omop_cdm -c "\COPY public.concept FROM 'CONCEPT.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"
   
   # Repeat for all vocabulary files:
   psql -U omop -d omop_cdm -c "\COPY public.vocabulary FROM 'VOCABULARY.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"
   psql -U omop -d omop_cdm -c "\COPY public.domain FROM 'DOMAIN.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"
   psql -U omop -d omop_cdm -c "\COPY public.concept_class FROM 'CONCEPT_CLASS.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"
   psql -U omop -d omop_cdm -c "\COPY public.concept_relationship FROM 'CONCEPT_RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"
   psql -U omop -d omop_cdm -c "\COPY public.relationship FROM 'RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"
   psql -U omop -d omop_cdm -c "\COPY public.concept_synonym FROM 'CONCEPT_SYNONYM.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"
   psql -U omop -d omop_cdm -c "\COPY public.concept_ancestor FROM 'CONCEPT_ANCESTOR.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"
   psql -U omop -d omop_cdm -c "\COPY public.source_to_concept_map FROM 'SOURCE_TO_CONCEPT_MAP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"
   psql -U omop -d omop_cdm -c "\COPY public.drug_strength FROM 'DRUG_STRENGTH.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"
   ```

> **Note**: Loading vocabularies can take a significant amount of time, especially for large vocabulary files like CONCEPT.csv.

## Step 9: Verify Vocabulary Loading

Check that the vocabularies were loaded correctly:

```bash
# Count the number of concepts
psql -U omop -d omop_cdm -c "SELECT COUNT(*) FROM concept;"

# Check available vocabularies
psql -U omop -d omop_cdm -c "SELECT vocabulary_id, vocabulary_name, vocabulary_version FROM vocabulary;"
```

## Troubleshooting

### Connection Issues
- Verify that PostgreSQL is running: `sudo systemctl status postgresql`
- Check that you can connect with the omop user: `psql -U omop -d omop_cdm`
- Ensure your firewall allows connections to port 5432

### Permission Errors
- Verify that the omop user has the necessary permissions: 
  ```sql
  \c omop_cdm
  \dn+
  ```

### Schema Issues
- If you're using a custom schema, make sure it exists and the user has permissions:
  ```sql
  \dn
  ```

### Out of Disk Space
- Check available disk space: `df -h`
- Vocabulary files can be several GB in size

### SQL Errors
- Check the PostgreSQL logs: `sudo tail -f /var/log/postgresql/postgresql-13-main.log`

## Performance Optimization (Optional)

For better performance, especially with large datasets, consider these PostgreSQL optimizations:

```sql
-- Adjust memory parameters
ALTER SYSTEM SET shared_buffers = '1GB';  -- 25% of RAM for dedicated server
ALTER SYSTEM SET work_mem = '64MB';       -- Increase for complex queries
ALTER SYSTEM SET maintenance_work_mem = '256MB';  -- For maintenance operations

-- Write-Ahead Log settings
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;

-- Autovacuum settings
ALTER SYSTEM SET autovacuum_vacuum_scale_factor = 0.05;
ALTER SYSTEM SET autovacuum_analyze_scale_factor = 0.025;

-- Apply changes
SELECT pg_reload_conf();
```

## Next Steps

After setting up the database:

1. Configure your ETL process to transform FHIR data to OMOP
2. Consider running data quality checks using the [OHDSI Data Quality Dashboard](https://github.com/OHDSI/DataQualityDashboard)
3. Explore your data with [ATLAS](https://github.com/OHDSI/Atlas) or [ACHILLES](https://github.com/OHDSI/Achilles)

## References

1. [OHDSI Common Data Model](https://ohdsi.github.io/CommonDataModel/)
2. [The Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/)
3. [OHDSI GitHub Repository](https://github.com/OHDSI/CommonDataModel)
4. [PostgreSQL Documentation](https://www.postgresql.org/docs/)
