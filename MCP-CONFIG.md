# MCP Configuration Guide

## Overview
This project uses Model Context Protocol (MCP) servers across two IDEs with a clean separation of concerns.

## Configuration Structure

### Global Cursor (`~/.cursor/mcp.json`)
**Universal tools available across all projects**
- `github`: Repository management (repos, issues, prs)
- `browsermcp`: Web browser automation (disabled by default)

### Project-Specific (`.vscode/mcp.json`)
**FHIR-OMOP specific tools**
- `database-omop-local`: Local OMOP database (port 5432)
- `database-hapi-docker`: Docker HAPI FHIR server (port 5433)
- `web-research`: Perplexity API for research queries

## Usage Guidelines

### When to Enable/Disable
- **Always on**: `github` (global), `database-omop-local` (project)  
- **Enable when needed**: `browsermcp`, `web-research`
- **Enable for FHIR work**: `database-hapi-docker`

### Tool Limits
- Cursor supports max 128 tools across all servers
- Current setup: ~60 tools (safe margin)

### Quick Commands
```bash
# Edit configurations
alias mcp-global="code ~/.cursor/mcp.json"
alias mcp-project="code ./.vscode/mcp.json"

# Check available configs  
alias mcp-list="find ~ -name '*mcp*.json' -type f"
```

## Environment Variables Required
- `GITHUB_PERSONAL_ACCESS_TOKEN`: Set in `~/.zshrc`
- `PERPLEXITY_API_KEY`: Prompted when needed (project input)

## Troubleshooting
- **128 tools error**: Disable unused servers with `"disabled": true`
- **Docker issues**: Ensure Docker Desktop is running
- **Database connection**: Check PostgreSQL services are active
